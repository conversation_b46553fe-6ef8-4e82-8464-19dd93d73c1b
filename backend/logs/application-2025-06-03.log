{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:31:20:3120"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:31:21:3121"}
{"error":"Weaviate client not initialized. Call connect() first.","level":"error","message":"Weaviate health check failed","stack":"Error: Weaviate client not initialized. Call connect() first.\n    at WeaviateConnection.getClient (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:51:13)\n    at WeaviateConnection.healthCheck (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:388:27)\n    at WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:27:18)\n    at connectWeaviate (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:466:57)\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:31:21:3121"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:315:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:33:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:31:21:3121"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:32:22:3222"}
{"error":"Weaviate client not initialized. Call connect() first.","level":"error","message":"Weaviate health check failed","stack":"Error: Weaviate client not initialized. Call connect() first.\n    at WeaviateConnection.getClient (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:55:13)\n    at WeaviateConnection.healthCheck (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:392:27)\n    at WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:27:36)\n    at connectWeaviate (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:470:57)\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"warn","message":"⚠️ Weaviate health check failed, but continuing without vector database","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:32:22:3222"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:32:23:3223"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:32:33:3233"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:32:35:3235"}
{"error":"Weaviate client not initialized. Vector database features are not available.","level":"error","message":"Weaviate health check failed","stack":"Error: Weaviate client not initialized. Vector database features are not available.\n    at WeaviateConnection.getClient (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:55:13)\n    at WeaviateConnection.healthCheck (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:392:27)\n    at WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:27:36)\n    at connectWeaviate (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:470:57)\n    at Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"warn","message":"⚠️ Weaviate health check failed, but continuing without vector database","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:32:35:3235"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:32:46:3246"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:32:47:3247"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:47:3247"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:157:7)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:178:7)","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:32:47:3247"}
{"level":"info","message":"📴 Received SIGTERM. Starting graceful shutdown...","timestamp":"2025-06-03 23:33:07:337"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:33:08:338"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:08:338"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:08:338"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:33:08:338"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:33:18:3318"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:18:3318"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:33:18:3318"}
{"level":"info","message":"Initializing database connections...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to Neo4j database...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Neo4j connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Initializing Neo4j schema...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Neo4j schema initialized successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Neo4j connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to Weaviate vector database...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Weaviate connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Initializing Weaviate schemas...","timestamp":"2025-06-03 23:34:04:344"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"Failed to create schema: Supplement","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:34:04:344"}
{"error":"usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","level":"error","message":"❌ Failed to initialize Weaviate schemas","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:34:04:344"}
{"level":"warn","message":"⚠️ Failed to connect to Weaviate, continuing without vector database: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}","stack":"Error: usage error (401): {\"code\":401,\"message\":\"oidc auth is not configured, please try another auth scheme or set up weaviate with OIDC configured\"}\n    at /home/<USER>/Suplementor/backend/node_modules/weaviate-ts-client/dist/index.js:1:9066\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async WeaviateConnection.initializeSchemas (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:319:26)\n    at async WeaviateConnection.connect (/home/<USER>/Suplementor/backend/src/config/weaviate.ts:37:7)\n    at async Application.initializeDatabases (/home/<USER>/Suplementor/backend/src/index.ts:158:9)\n    at async Application.start (/home/<USER>/Suplementor/backend/src/index.ts:182:7)","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Weaviate connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to Redis cache...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Redis client connected","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Redis client ready","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Redis connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ Redis connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"Connecting to MongoDB...","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ MongoDB connection established successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"✅ MongoDB connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🚀 All databases connected successfully","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🚀 Server running on port 3000","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🔗 API URL: http://localhost:3000/api","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"🔌 AG-UI WebSocket: ws://localhost:3000/agui/ws","timestamp":"2025-06-03 23:34:04:344"}
{"level":"info","message":"💚 Health check: http://localhost:3000/health","timestamp":"2025-06-03 23:34:04:344"}
